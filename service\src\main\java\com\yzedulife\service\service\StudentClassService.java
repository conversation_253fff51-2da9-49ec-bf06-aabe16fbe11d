package com.yzedulife.service.service;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentClassDTO;

import java.util.List;

/**
 * 学生班级服务接口
 */
public interface StudentClassService {

    /**
     * 根据ID获取班级
     */
    StudentClassDTO getById(Long id) throws BusinessException;

    /**
     * 根据班级名称获取班级
     */
    StudentClassDTO getByClassName(String className) throws BusinessException;

    /**
     * 创建班级
     */
    StudentClassDTO create(StudentClassDTO studentClassDTO) throws BusinessException;

    /**
     * 更新班级
     */
    StudentClassDTO update(StudentClassDTO studentClassDTO) throws BusinessException;

    /**
     * 删除班级（同时删除该班级的所有学生）
     */
    Boolean deleteById(Long id) throws BusinessException;

    /**
     * 获取所有班级
     */
    List<StudentClassDTO> getAll() throws BusinessException;

    /**
     * 检查班级是否存在
     */
    Boolean isExist(Long id) throws BusinessException;

    /**
     * 检查班级名称是否存在
     */
    Boolean isClassNameExist(String className) throws BusinessException;
}
