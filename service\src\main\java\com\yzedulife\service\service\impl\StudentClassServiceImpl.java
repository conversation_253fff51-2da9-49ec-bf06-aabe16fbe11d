package com.yzedulife.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.service.entity.StudentClass;
import com.yzedulife.service.entity.StudentUser;
import com.yzedulife.service.mapper.StudentClassMapper;
import com.yzedulife.service.mapper.StudentUserMapper;
import com.yzedulife.service.service.StudentClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 学生班级服务实现类
 */
@Service
@Transactional
public class StudentClassServiceImpl implements StudentClassService {

    @Autowired
    private StudentClassMapper studentClassMapper;

    @Autowired
    private StudentUserMapper studentUserMapper;

    @Override
    public StudentClassDTO getById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("班级ID不能为空");
        }
        StudentClass studentClass = studentClassMapper.selectById(id);
        if (studentClass == null) {
            throw new BusinessException("班级不存在");
        }
        return studentClass.toDTO();
    }

    @Override
    public StudentClassDTO getByClassName(String className) throws BusinessException {
        if (!StringUtils.hasText(className)) {
            throw new BusinessException("班级名称不能为空");
        }
        StudentClass studentClass = studentClassMapper.selectOne(new LambdaQueryWrapper<StudentClass>()
                .eq(StudentClass::getClassName, className));
        if (studentClass == null) {
            throw new BusinessException("班级不存在");
        }
        return studentClass.toDTO();
    }

    @Override
    public StudentClassDTO create(StudentClassDTO studentClassDTO) throws BusinessException {
        if (studentClassDTO == null) {
            throw new BusinessException("班级信息不能为空");
        }
        if (!StringUtils.hasText(studentClassDTO.getClassName())) {
            throw new BusinessException("班级名称不能为空");
        }
        
        // 检查班级名称是否已存在
        if (isClassNameExist(studentClassDTO.getClassName())) {
            throw new BusinessException("班级名称已存在");
        }
        
        StudentClass studentClass = studentClassDTO.toEntity();
        studentClass.setId(null); // 确保是新增
        int result = studentClassMapper.insert(studentClass);
        if (result <= 0) {
            throw new BusinessException("创建班级失败");
        }
        return studentClass.toDTO();
    }

    @Override
    public StudentClassDTO update(StudentClassDTO studentClassDTO) throws BusinessException {
        if (studentClassDTO == null || studentClassDTO.getId() == null) {
            throw new BusinessException("班级ID不能为空");
        }
        
        // 检查班级是否存在
        StudentClass existingClass = studentClassMapper.selectById(studentClassDTO.getId());
        if (existingClass == null) {
            throw new BusinessException("班级不存在");
        }
        
        // 如果修改了班级名称，检查新名称是否已存在
        if (StringUtils.hasText(studentClassDTO.getClassName()) && 
            !studentClassDTO.getClassName().equals(existingClass.getClassName())) {
            if (isClassNameExist(studentClassDTO.getClassName())) {
                throw new BusinessException("班级名称已存在");
            }
        }
        
        // 更新字段
        if (StringUtils.hasText(studentClassDTO.getClassName())) {
            existingClass.setClassName(studentClassDTO.getClassName());
        }
        
        int result = studentClassMapper.updateById(existingClass);
        if (result <= 0) {
            throw new BusinessException("更新班级失败");
        }
        return existingClass.toDTO();
    }

    @Override
    public Boolean deleteById(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("班级ID不能为空");
        }
        
        StudentClass studentClass = studentClassMapper.selectById(id);
        if (studentClass == null) {
            throw new BusinessException("班级不存在");
        }
        
        // 删除该班级的所有学生
        studentUserMapper.delete(new LambdaQueryWrapper<StudentUser>()
                .eq(StudentUser::getClassId, id));
        
        // 删除班级
        int result = studentClassMapper.deleteById(id);
        return result > 0;
    }

    @Override
    public List<StudentClassDTO> getAll() throws BusinessException {
        List<StudentClass> studentClasses = studentClassMapper.selectList(new LambdaQueryWrapper<StudentClass>()
                .orderByDesc(StudentClass::getId));
        return studentClasses.stream().map(StudentClass::toDTO).collect(java.util.stream.Collectors.toList());
    }

    @Override
    public Boolean isExist(Long id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("班级ID不能为空");
        }
        return studentClassMapper.exists(new LambdaQueryWrapper<StudentClass>()
                .eq(StudentClass::getId, id));
    }

    @Override
    public Boolean isClassNameExist(String className) throws BusinessException {
        if (!StringUtils.hasText(className)) {
            return false;
        }
        return studentClassMapper.exists(new LambdaQueryWrapper<StudentClass>()
                .eq(StudentClass::getClassName, className));
    }
}
