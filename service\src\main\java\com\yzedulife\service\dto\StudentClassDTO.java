package com.yzedulife.service.dto;

import com.yzedulife.service.convert.StudentClassConvert;
import com.yzedulife.service.entity.StudentClass;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 学生班级DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StudentClassDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 班级ID
     */
    private Long id;

    /**
     * 班级名称
     */
    private String className;

    public StudentClass toEntity() {
        return StudentClassConvert.INSTANCE.dto2entity(this);
    }
}
