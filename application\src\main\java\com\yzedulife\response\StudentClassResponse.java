package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 学生班级响应（包含学生列表）
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "学生班级响应")
public class StudentClassResponse {

    @Schema(description = "班级ID")
    private Long id;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "班级学生列表")
    private List<StudentUserResponse> students;
}
